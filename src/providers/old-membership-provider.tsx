import { createContext, useContext, useEffect, useState } from 'react';
import { useAccount } from 'wagmi';

import { IPresale, Membership } from '@/class/interface/presale';
import { PublicClientContext } from './public-client-provider';
import { OldPresaleV3 } from '../class/old-presale-v3';
import { OldPresaleV4 } from '@/class/old-presale-v4';

// Old presale contract configuration
// TODO: Replace with actual old contract address and version before deployment
const OLD_PRESALE_CONTRACT_ADDRESS = '0x04aAC06e8f5f2D6f76177819C9fd69736FdbF7f2' as const;
const OLD_PRESALE_VERSION: 'v3' | 'v4' = 'v3'; // Change to 'v4' if old contract uses v4

interface OldMembershipsContextType {
  memberships: Membership[];
  selectedMembershipId: string;
  loading: boolean;
  error: string | null;
  fetchMemberships: () => Promise<void>;
  handleMembershipChange: (membershipId: string) => void;
}

export const OldMembershipsContext = createContext<OldMembershipsContextType>({
  memberships: [],
  selectedMembershipId: '',
  loading: false,
  error: null,
  fetchMemberships: async () => {},
  handleMembershipChange: () => {},
});

export function OldMembershipsProvider({ children }: { children: React.ReactNode }) {
  const [memberships, setMemberships] = useState<Membership[]>([]);
  const [selectedMembershipId, setSelectedMembershipId] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [oldPresaleInstance, setOldPresaleInstance] = useState<IPresale | null>(null);

  const { address } = useAccount();
  const { publicClient } = useContext(PublicClientContext);

  // Initialize old presale instance
  useEffect(() => {
    const initializeOldPresale = async () => {
      try {

        // Use appropriate old presale version based on configuration
        const instance = OLD_PRESALE_VERSION === 'v4'
          ? await OldPresaleV4.createInstance(publicClient, OLD_PRESALE_CONTRACT_ADDRESS)
          : await OldPresaleV3.createInstance(publicClient, OLD_PRESALE_CONTRACT_ADDRESS);

        setOldPresaleInstance(instance);
        setError(null);
      } catch (err) {
        console.error('Failed to initialize old presale instance:', err);
        setError('Failed to connect to old presale contract');
        setLoading(false);
      }
    };

    if (publicClient) {
      initializeOldPresale();
    }
  }, [publicClient]);

  // Fetch memberships from old contract
  const fetchMemberships = async () => {
    if (!address || !oldPresaleInstance) {
      setMemberships([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const oldMemberships = await oldPresaleInstance.getMemberships(address);
      
      setMemberships(oldMemberships);
      
      // Auto-select first membership if none selected
      if (oldMemberships.length > 0 && !selectedMembershipId) {
        setSelectedMembershipId(oldMemberships[0].id);
      }
    } catch (err) {
      console.error('Failed to fetch old memberships:', err);
      setError('Failed to fetch memberships from old contract');
      setMemberships([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle membership selection change
  const handleMembershipChange = (membershipId: string) => {
    setSelectedMembershipId(membershipId);
  };

  // Fetch memberships when dependencies are ready
  useEffect(() => {
    if (oldPresaleInstance && address) {
      fetchMemberships();
    }
  }, [oldPresaleInstance, address]);

  // Reset when address changes
  useEffect(() => {
    if (!address) {
      setMemberships([]);
      setSelectedMembershipId('');
      setError(null);
      setLoading(false);
    }
  }, [address]);

  const contextValue: OldMembershipsContextType = {
    memberships,
    selectedMembershipId,
    loading,
    error,
    fetchMemberships,
    handleMembershipChange,
  };

  return (
    <OldMembershipsContext.Provider value={contextValue}>
      {children}
    </OldMembershipsContext.Provider>
  );
}
