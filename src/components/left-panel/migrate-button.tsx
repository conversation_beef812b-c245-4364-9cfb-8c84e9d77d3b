import { useContext, useState } from 'react';

import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { OldMembershipsContext } from '@/providers/old-membership-provider';
import { Migrate } from '../vest-panel/migrate';

export default function MigrateButton() {
  const [isOpen, setIsOpen] = useState(false);
  const { memberships: oldMemberships, loading } = useContext(OldMembershipsContext);

  const hasOldMemberships = !loading && oldMemberships.length > 0 && oldMemberships[0];

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="flex flex-row-reverse gap-2"
          aria-label="Migrate from old contract"
          title="Migrate from old contract"
        >
          Migrate
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto p-[1px] bg-gradient-to-br from-[#f106b4] to-[#0099fe] rounded-lg">
        <div className="bg-black w-full h-full text-white rounded-lg p-6 overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Migrate</DialogTitle>
          <DialogDescription>
            {hasOldMemberships 
              ? "Migrate from the old contract to the new contract."
              : loading
              ? "Loading migration data..."
              : "No migration available from the old contract."
            }
          </DialogDescription>
        </DialogHeader>
        <div className="mt-4">
          {hasOldMemberships ? (
            <div className="space-y-4">
              <div className="bg-blue-900/20 border border-blue-700/50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-blue-400 mb-2">Migration Information</h4>
                <div className="text-sm text-gray-300 space-y-3">
                  <p>Only whitelisted people can migrate from the old presale contract to the new contract.</p>
                  <p><strong>NOTE:</strong> Once you click migrate you need to complete 2x sign transactions on Metamask/your wallet to confirm your migration to new presale smart contract.</p>
                  <p>After migration completed you can connect your TGLP subscriber wallet into the new presale round here <a href="https://presale.raiinmaker.com/" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-300 underline">https://presale.raiinmaker.com/</a> to check your allocation purchased etc.</p>
                </div>
              </div>
              <Migrate membership={oldMemberships[0]} />
            </div>
          ) : (
            <div className="text-center py-8 text-gray-400">
              {loading ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="animate-spin h-4 w-4 border-2 border-gray-400 border-t-transparent rounded-full"></div>
                  <span>Checking for migration data...</span>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-300 mb-2">About Migration</h4>
                    <div className="text-sm text-gray-400 space-y-2">
                      <p>Migration allows users to transfer from old presale contracts to new ones.</p>
                      <p>This ensures you can access the latest features and improvements.</p>
                    </div>
                  </div>
                  <div>
                    <p className="mb-2 text-gray-300">No migration available</p>
                    <p className="text-sm">You don't have anything in the old contract that can be migrated.</p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}