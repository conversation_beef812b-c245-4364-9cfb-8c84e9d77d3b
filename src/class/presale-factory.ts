import { Address, Client } from 'viem';

import { IPresale } from './interface/presale';
import { PresaleV3 } from './presale-v3';
import { PresaleV4 } from './presale-v4';
import { PresaleV5 } from './presale-v5';

export type PresaleVersion = 'v3' | 'v4' | 'v5';

export class PresaleFactory {
  public static async createInstance(
    version: PresaleVersion,
    publicClient: Client,
    presaleContractAddress: Address,
  ): Promise<IPresale> {
    switch (version) {
      case 'v3':
        return PresaleV3.createInstance(publicClient, presaleContractAddress);
      case 'v4':
        return PresaleV4.createInstance(publicClient, presaleContractAddress);
      case 'v5':
        return PresaleV5.createInstance(publicClient, presaleContractAddress);
        default:
        throw new Error('Unsupported version');
    }
  }
}
